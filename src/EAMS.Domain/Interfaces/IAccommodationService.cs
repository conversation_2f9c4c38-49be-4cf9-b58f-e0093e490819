using EAMS.Domain.Entities;

namespace EAMS.Domain.Interfaces
{
    public interface IAccommodationService
    {
        Task<IEnumerable<Accommodation>> GetAllAccommodationsAsync(CancellationToken cancellationToken = default);
        Task<Accommodation?> GetAccommodationByIdAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<Accommodation> CreateAccommodationAsync(Accommodation accommodation, CancellationToken cancellationToken = default);
        Task<Accommodation> UpdateAccommodationAsync(Int64 id, Accommodation accommodation, CancellationToken cancellationToken = default);
        Task<bool> DeleteAccommodationAsync(Int64 id, CancellationToken cancellationToken = default);
        Task<bool> AccommodationExistsAsync(Int64 id, CancellationToken cancellationToken = default);
    }
}
