using EAMS.Domain.Entities;
using EAMS.Domain.Interfaces;

namespace EAMS.Domain.Services
{
    public class AccommodationService : IAccommodationService
    {
        private readonly IAccommodationRepository _accommodationRepository;

        public AccommodationService(IAccommodationRepository accommodationRepository)
        {
            _accommodationRepository = accommodationRepository;
        }

        public async Task<IEnumerable<Accommodation>> GetAllAccommodationsAsync(CancellationToken cancellationToken = default)
        {
            return await _accommodationRepository.GetAllAsync(cancellationToken);
        }

        public async Task<Accommodation?> GetAccommodationByIdAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _accommodationRepository.GetByIdAsync(id, cancellationToken);
        }

        public async Task<Accommodation> CreateAccommodationAsync(Accommodation accommodation, CancellationToken cancellationToken = default)
        {
            var createdAccommodation = await _accommodationRepository.AddAsync(accommodation, cancellationToken);
            await _accommodationRepository.SaveChangesAsync(cancellationToken);
            return createdAccommodation;
        }

        public async Task<Accommodation> UpdateAccommodationAsync(Int64 id, Accommodation accommodation, CancellationToken cancellationToken = default)
        {
            var existingAccommodation = await _accommodationRepository.GetByIdAsync(id, cancellationToken);
            if (existingAccommodation == null)
            {
                throw new InvalidOperationException($"Accommodation with ID {id} not found");
            }

            accommodation.Id = id; // Ensure the ID is preserved
            var result = await _accommodationRepository.UpdateAsync(accommodation, cancellationToken);
            await _accommodationRepository.SaveChangesAsync(cancellationToken);
            return result;
        }

        public async Task<bool> DeleteAccommodationAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            var result = await _accommodationRepository.DeleteAsync(id, cancellationToken);
            if (result)
            {
                await _accommodationRepository.SaveChangesAsync(cancellationToken);
            }
            return result;
        }

        public async Task<bool> AccommodationExistsAsync(Int64 id, CancellationToken cancellationToken = default)
        {
            return await _accommodationRepository.ExistsAsync(id, cancellationToken);
        }
    }
}
